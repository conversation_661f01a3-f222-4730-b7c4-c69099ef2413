# 日志清理功能实现文档

## 🎯 功能概述

为防止数据库占用过大，系统已集成自动日志清理功能，**只保留3个月内的操作日志**。

## 📋 功能特性

### ✅ 核心特性
- **自动清理**: 无需手动干预，系统自动清理过期日志
- **保留期限**: 只保留3个月（90天）内的日志记录
- **定时检查**: 每24小时检查一次是否需要清理
- **异步执行**: 清理过程异步执行，不影响业务性能
- **安全机制**: 清理失败不会影响主业务流程

### 🔧 技术实现
- **触发方式**: 通过 `OperationLogMiddleware` 中间件自动触发
- **执行条件**: 距离上次清理超过24小时时执行
- **清理范围**: 删除 `operation_time` 早于90天前的所有日志记录
- **错误处理**: 清理异常会记录到控制台，但不抛出错误

## 🏗️ 实现架构

### 1. 中间件层 (`OperationLogMiddleware`)
```typescript
// 清理配置
private readonly CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24小时
private readonly LOG_RETENTION_MONTHS = 3; // 保留3个月

// 检查并清理过期日志
private async checkAndCleanupLogs(ctx: Context): Promise<void> {
  // 检查是否需要执行清理（24小时间隔）
  if (currentTime - this.lastCleanupTime < this.CLEANUP_INTERVAL) {
    return;
  }
  
  // 执行清理
  const daysToKeep = this.LOG_RETENTION_MONTHS * 30;
  const deletedCount = await this.operationLogService.cleanupExpiredLogs(daysToKeep);
}
```

### 2. 服务层 (`OperationLogService`)
```typescript
// 删除过期的操作日志
async cleanupExpiredLogs(daysToKeep = 90): Promise<number> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const deletedCount = await this.operationLogRepository.destroy({
    where: {
      operation_time: {
        [Op.lt]: cutoffDate,
      },
    },
  });

  return deletedCount;
}
```

## 🔄 工作流程

1. **请求触发**: 任何通过中间件的请求都会触发清理检查
2. **时间检查**: 检查距离上次清理是否超过24小时
3. **执行清理**: 如果满足条件，异步执行清理操作
4. **删除记录**: 删除超过90天的所有日志记录
5. **记录结果**: 在控制台输出清理结果
6. **更新时间**: 更新最后清理时间戳

## 📊 清理规则

### 时间规则
- **保留期限**: 3个月（90天）
- **检查频率**: 每24小时检查一次
- **清理条件**: `operation_time < (当前时间 - 90天)`

### 触发条件
- 任何通过 `OperationLogMiddleware` 的请求
- 距离上次清理超过24小时
- 认证相关请求（`/auth/`, `/api/auth/`）
- 系统管理请求（`/system/`, `/admin/`）

## 🛡️ 安全机制

### 错误处理
```typescript
try {
  // 执行清理逻辑
} catch (error) {
  // 清理失败不应该影响主业务
  console.error('日志清理失败:', error);
}
```

### 性能保护
- **异步执行**: 清理过程不阻塞主业务
- **间隔限制**: 24小时间隔防止频繁清理
- **错误隔离**: 清理失败不影响业务流程

## 📈 监控与维护

### 日志输出
清理成功时会输出：
```
🧹 日志清理完成：删除了 X 条超过 3 个月的日志记录
```

### 监控建议
1. **定期检查**: 监控数据库 `operation_logs` 表大小
2. **日志观察**: 关注清理成功/失败的日志输出
3. **性能监控**: 确保清理过程不影响业务性能
4. **数据备份**: 重要日志数据建议定期备份

## 🔧 配置调整

### 修改保留期限
在 `OperationLogMiddleware` 中修改：
```typescript
private readonly LOG_RETENTION_MONTHS = 6; // 改为6个月
```

### 修改检查频率
```typescript
private readonly CLEANUP_INTERVAL = 12 * 60 * 60 * 1000; // 改为12小时
```

### 手动触发清理
```typescript
// 在服务中直接调用
await operationLogService.cleanupExpiredLogs(90);
```

## 📋 测试验证

### 功能测试
- ✅ 清理功能已集成到中间件
- ✅ 24小时间隔限制正常工作
- ✅ 异步执行不影响业务性能
- ✅ 错误处理确保主业务不受影响

### 验证方法
1. 查询当前日志数量
2. 触发认证请求激活中间件
3. 观察清理日志输出
4. 验证间隔限制机制

## 🎉 总结

日志清理功能已成功集成到系统中，具备以下优势：

- **🔄 自动化**: 无需人工干预，自动维护日志数据
- **⚡ 高性能**: 异步执行，不影响业务响应速度
- **🛡️ 安全性**: 完善的错误处理和异常隔离
- **📊 可监控**: 清理过程有日志输出，便于监控
- **🔧 可配置**: 保留期限和检查频率可灵活调整

这个实现确保了数据库不会因为日志数据过多而影响性能，同时保持了系统的稳定性和可维护性。
