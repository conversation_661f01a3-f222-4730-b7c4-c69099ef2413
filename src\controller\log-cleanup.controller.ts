import {
  Controller,
  Get,
  Post,
  Inject,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { BaseController } from '../common/BaseController';
import { LogCleanupTask } from '../task/log-cleanup.task';
import { ApiResponseUtil } from '../common/ApiResponse';

/**
 * 日志清理管理控制器
 */
@Controller('/api/log-cleanup')
export class LogCleanupController extends BaseController {
  @Inject()
  ctx: Context;

  @Inject()
  logCleanupTask: LogCleanupTask;

  /**
   * 获取清理任务配置信息
   */
  @Get('/info')
  async getTaskInfo() {
    try {
      const taskInfo = this.logCleanupTask.getTaskInfo();
      
      return ApiResponseUtil.success(taskInfo, '获取清理任务信息成功');
    } catch (error) {
      this.ctx.logger.error('获取清理任务信息失败:', error);
      return ApiResponseUtil.error(500, '获取清理任务信息失败');
    }
  }

  /**
   * 手动触发清理任务
   */
  @Post('/manual')
  async manualCleanup() {
    try {
      this.ctx.logger.info('手动触发日志清理任务', {
        operator: this.getCurrentUserId(),
        timestamp: new Date().toISOString(),
      });

      const result = await this.logCleanupTask.manualCleanup();
      
      // 记录操作日志
      await this.logOperation('手动清理日志', {
        deletedCount: result.deletedCount,
        duration: result.duration,
      });

      return ApiResponseUtil.success(result, '手动清理任务执行成功');
    } catch (error) {
      this.ctx.logger.error('手动清理任务执行失败:', error);
      
      // 记录操作日志
      await this.logOperation('手动清理日志失败', {
        error: error.message,
      });

      return ApiResponseUtil.error(500, '手动清理任务执行失败: ' + error.message);
    }
  }

  /**
   * 获取清理任务状态和统计信息
   */
  @Get('/status')
  async getCleanupStatus() {
    try {
      const taskInfo = this.logCleanupTask.getTaskInfo();
      
      // 计算下次执行时间
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(2, 0, 0, 0); // 设置为明天凌晨2点
      
      const nextExecution = tomorrow.toISOString();
      const timeUntilNext = tomorrow.getTime() - now.getTime();
      const hoursUntilNext = Math.floor(timeUntilNext / (1000 * 60 * 60));
      const minutesUntilNext = Math.floor((timeUntilNext % (1000 * 60 * 60)) / (1000 * 60));

      const status = {
        ...taskInfo,
        currentTime: now.toISOString(),
        nextExecution,
        timeUntilNext: `${hoursUntilNext}小时${minutesUntilNext}分钟`,
        isActive: true,
        lastManualExecution: null, // 可以扩展为记录最后一次手动执行时间
      };

      return ApiResponseUtil.success(status, '获取清理任务状态成功');
    } catch (error) {
      this.ctx.logger.error('获取清理任务状态失败:', error);
      return ApiResponseUtil.error(500, '获取清理任务状态失败');
    }
  }

  /**
   * 获取清理历史统计
   */
  @Get('/history')
  async getCleanupHistory() {
    try {
      // 这里可以扩展为从数据库查询清理历史记录
      // 目前返回模拟数据作为示例
      const history = {
        totalExecutions: 0,
        totalDeletedRecords: 0,
        lastExecution: null,
        averageDuration: 0,
        successRate: 100,
        recentExecutions: [], // 最近的执行记录
      };

      return ApiResponseUtil.success(history, '获取清理历史成功');
    } catch (error) {
      this.ctx.logger.error('获取清理历史失败:', error);
      return ApiResponseUtil.error(500, '获取清理历史失败');
    }
  }

  /**
   * 获取清理规则说明
   */
  @Get('/rules')
  async getCleanupRules() {
    try {
      const rules = {
        retentionPolicy: {
          days: 90,
          description: '只保留90天（3个月）内的日志记录',
        },
        schedule: {
          cron: '0 0 2 * * *',
          description: '每天凌晨2点自动执行清理',
          timezone: 'Asia/Shanghai',
        },
        scope: {
          tables: ['operation_logs'],
          criteria: 'operation_time < (当前时间 - 90天)',
          description: '清理operation_logs表中超过90天的记录',
        },
        safety: {
          asyncExecution: true,
          errorHandling: true,
          businessImpact: false,
          description: '异步执行，错误隔离，不影响业务',
        },
        monitoring: {
          logging: true,
          statistics: true,
          alerts: false,
          description: '记录执行日志和统计信息',
        },
      };

      return ApiResponseUtil.success(rules, '获取清理规则成功');
    } catch (error) {
      this.ctx.logger.error('获取清理规则失败:', error);
      return ApiResponseUtil.error(500, '获取清理规则失败');
    }
  }
}
