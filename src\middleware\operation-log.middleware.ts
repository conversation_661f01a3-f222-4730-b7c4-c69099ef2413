import { Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';
import { OperationLogService } from '../service/operation-log.service';
import {
  OperationModule,
  OperationType,
  OperationStatus,
} from '../entity/operation-log.entity';

@Middleware()
export class OperationLogMiddleware {
  private operationLogService: OperationLogService;
  private lastCleanupTime = 0;
  private readonly CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24小时检查一次
  private readonly LOG_RETENTION_MONTHS = 3; // 保留3个月

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const startTime = Date.now();
      let operationStatus = OperationStatus.SUCCESS;
      let errorMessage: string | undefined;

      try {
        // 检查是否需要清理过期日志
        await this.checkAndCleanupLogs(ctx);

        await next();
      } catch (error) {
        operationStatus = OperationStatus.FAILED;
        errorMessage = error.message;
        throw error;
      } finally {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // 只记录特定的API操作
        if (this.shouldLogOperation(ctx)) {
          await this.logOperation(
            ctx,
            operationStatus,
            errorMessage,
            responseTime
          );
        }
      }
    };
  }

  /**
   * 检查并清理过期日志
   * @param ctx Koa上下文
   */
  private async checkAndCleanupLogs(ctx: Context): Promise<void> {
    try {
      const currentTime = Date.now();

      // 检查是否需要执行清理（24小时检查一次）
      if (currentTime - this.lastCleanupTime < this.CLEANUP_INTERVAL) {
        return;
      }

      // 更新最后清理时间
      this.lastCleanupTime = currentTime;

      // 获取OperationLogService实例
      if (!this.operationLogService) {
        this.operationLogService = await ctx.requestContext.getAsync(
          OperationLogService
        );
      }

      // 计算3个月的天数（约90天）
      const daysToKeep = this.LOG_RETENTION_MONTHS * 30;

      // 执行清理
      const deletedCount = await this.operationLogService.cleanupExpiredLogs(
        daysToKeep
      );

      if (deletedCount > 0) {
        console.log(
          `🧹 日志清理完成：删除了 ${deletedCount} 条超过 ${this.LOG_RETENTION_MONTHS} 个月的日志记录`
        );
      }
    } catch (error) {
      // 清理失败不应该影响主业务
      console.error('日志清理失败:', error);
    }
  }

  /**
   * 判断是否需要记录操作日志
   * @param ctx Koa上下文
   * @returns 是否需要记录
   */
  private shouldLogOperation(ctx: Context): boolean {
    const path = ctx.request.path;

    // 中间件只记录认证和系统相关的操作
    const authAndSystemPaths = [
      '/auth/', // 认证相关：登录、登出、token刷新
      '/api/auth/', // 认证API
      '/system/', // 系统管理相关
      '/admin/', // 管理员操作
    ];

    // 排除不需要记录的路径
    const excludePaths = [
      '/api/operation-log', // 操作日志接口本身
      '/health',
      '/favicon.ico',
      '/openapi',
      '/dev-tools',
    ];

    // 如果是排除路径，不记录
    if (excludePaths.some(excludePath => path.startsWith(excludePath))) {
      return false;
    }

    // 只记录认证和系统相关的操作
    // 所有业务操作（问卷、响应、统计等）由控制器层负责记录
    return authAndSystemPaths.some(authPath => path.startsWith(authPath));
  }

  /**
   * 记录操作日志
   * @param ctx Koa上下文
   * @param operationStatus 操作状态
   * @param errorMessage 错误信息
   * @param responseTime 响应时间
   */
  private async logOperation(
    ctx: Context,
    operationStatus: OperationStatus,
    errorMessage: string | undefined,
    responseTime: number
  ): Promise<void> {
    try {
      const operationLogService = await ctx.requestContext.getAsync(
        OperationLogService
      );

      // 从请求中获取操作用户信息（这里假设从header或body中获取）
      const operatorUserId = this.getOperatorUserId(ctx);

      if (!operatorUserId) {
        return; // 没有操作用户ID则不记录
      }

      const { module, operationType, description, targetId, targetType } =
        this.parseOperationInfo(ctx);

      await operationLogService.logOperation(
        ctx,
        operatorUserId,
        module,
        operationType,
        description,
        {
          targetId,
          targetType,
          requestParams: this.getRequestParams(ctx),
          operationStatus,
          errorMessage,
          responseTime,
          operatorUserName: this.getOperatorUserName(ctx),
          operatorUserRole: this.getOperatorUserRole(ctx),
          operatorSchoolCode: this.getOperatorSchoolCode(ctx),
        }
      );
    } catch (error) {
      // 记录日志失败不应该影响主业务
      console.error('操作日志中间件记录失败:', error);
    }
  }

  /**
   * 获取操作用户ID
   * @param ctx Koa上下文
   * @returns 操作用户ID
   */
  private getOperatorUserId(ctx: Context): string | undefined {
    // 优先从JWT认证的用户信息中获取
    const userInfo = ctx.state.user || {};
    let operatorUserId = userInfo.userId || userInfo.id;

    // 如果JWT中没有，尝试从header中获取
    if (!operatorUserId) {
      operatorUserId = ctx.request.headers['x-operator-user-id'] as string;
    }

    // 如果header中没有，尝试从body中获取
    if (
      !operatorUserId &&
      ctx.request.body &&
      typeof ctx.request.body === 'object'
    ) {
      const body = ctx.request.body as any;
      operatorUserId =
        body.operator_user_id || body.sso_student_id || body.sso_teacher_id;
    }

    // 如果还没有，尝试从query中获取
    if (!operatorUserId) {
      operatorUserId =
        (ctx.request.query.operator_user_id as string) ||
        (ctx.request.query.sso_student_id as string) ||
        (ctx.request.query.sso_teacher_id as string);
    }

    return operatorUserId;
  }

  /**
   * 获取操作用户姓名
   * @param ctx Koa上下文
   * @returns 操作用户姓名
   */
  private getOperatorUserName(ctx: Context): string | undefined {
    // 优先从JWT认证的用户信息中获取
    const userInfo = ctx.state.user || {};
    let operatorUserName = userInfo.userName || userInfo.name;

    // 如果JWT中没有，尝试从header中获取
    if (!operatorUserName) {
      operatorUserName = ctx.request.headers['x-operator-user-name'] as string;
    }

    // 如果header中没有，尝试从body中获取
    if (!operatorUserName) {
      const body = ctx.request.body as any;
      operatorUserName = body?.operator_user_name || body?.parent_name;
    }

    return operatorUserName;
  }

  /**
   * 获取操作用户角色
   * @param ctx Koa上下文
   * @returns 操作用户角色
   */
  private getOperatorUserRole(ctx: Context): string | undefined {
    const body = ctx.request.body as any;
    return (
      (ctx.request.headers['x-operator-user-role'] as string) ||
      body?.operator_user_role ||
      'parent'
    ); // 默认为家长角色
  }

  /**
   * 获取操作用户所属学校编码
   * @param ctx Koa上下文
   * @returns 学校编码
   */
  private getOperatorSchoolCode(ctx: Context): string | undefined {
    const body = ctx.request.body as any;
    return (
      (ctx.request.headers['x-operator-school-code'] as string) ||
      body?.sso_school_code ||
      (ctx.request.query?.sso_school_code as string)
    );
  }

  /**
   * 解析操作信息
   * @param ctx Koa上下文
   * @returns 操作信息
   */
  private parseOperationInfo(ctx: Context): {
    module: OperationModule;
    operationType: OperationType;
    description: string;
    targetId?: string;
    targetType?: string;
  } {
    const path = ctx.request.path;
    const method = ctx.request.method;

    // 认证相关操作
    if (path.startsWith('/auth/') || path.startsWith('/api/auth/')) {
      return this.parseAuthOperation(ctx, method, path);
    }

    // 系统管理操作
    if (path.startsWith('/system/') || path.startsWith('/admin/')) {
      return this.parseSystemOperation(ctx, method, path);
    }

    // 默认系统操作
    return {
      module: OperationModule.SYSTEM,
      operationType:
        method === 'GET' ? OperationType.QUERY : OperationType.UPDATE,
      description: `${method} ${path}`,
      targetId: undefined,
      targetType: 'system',
    };
  }

  /**
   * 解析认证操作信息
   */
  private parseAuthOperation(ctx: Context, method: string, path: string) {
    let operationType: OperationType;
    let description: string;

    if (path.includes('/login')) {
      operationType = OperationType.CREATE;
      description = '用户登录';
    } else if (path.includes('/logout')) {
      operationType = OperationType.DELETE;
      description = '用户登出';
    } else if (path.includes('/refresh')) {
      operationType = OperationType.UPDATE;
      description = '刷新令牌';
    } else if (path.includes('/register')) {
      operationType = OperationType.CREATE;
      description = '用户注册';
    } else {
      operationType =
        method === 'GET' ? OperationType.QUERY : OperationType.UPDATE;
      description = `认证操作: ${method} ${path}`;
    }

    return {
      module: OperationModule.SYSTEM,
      operationType,
      description,
      targetId: undefined,
      targetType: 'auth',
    };
  }

  /**
   * 解析系统管理操作信息
   */
  private parseSystemOperation(ctx: Context, method: string, path: string) {
    let operationType: OperationType;
    let description: string;

    if (path.includes('/config')) {
      operationType =
        method === 'GET' ? OperationType.QUERY : OperationType.UPDATE;
      description = method === 'GET' ? '查询系统配置' : '更新系统配置';
    } else if (path.includes('/user')) {
      operationType =
        method === 'GET' ? OperationType.QUERY : OperationType.UPDATE;
      description = method === 'GET' ? '查询用户管理' : '用户管理操作';
    } else {
      operationType =
        method === 'GET' ? OperationType.QUERY : OperationType.UPDATE;
      description = `系统管理: ${method} ${path}`;
    }

    return {
      module: OperationModule.SYSTEM,
      operationType,
      description,
      targetId: undefined,
      targetType: 'system',
    };
  }

  /**
   * 获取请求参数
   * @param ctx Koa上下文
   * @returns 请求参数
   */
  private getRequestParams(ctx: Context): any {
    const params: any = {};

    // 获取query参数
    if (Object.keys(ctx.request.query).length > 0) {
      params.query = ctx.request.query;
    }

    // 获取body参数（排除敏感信息）
    if (
      ctx.request.body &&
      typeof ctx.request.body === 'object' &&
      Object.keys(ctx.request.body).length > 0
    ) {
      const body = { ...(ctx.request.body as any) };

      // 移除敏感信息
      delete body.password;
      delete body.token;
      delete body.secret;

      params.body = body;
    }

    // 获取路径参数
    if (ctx.params && Object.keys(ctx.params).length > 0) {
      params.params = ctx.params;
    }

    return Object.keys(params).length > 0 ? params : undefined;
  }
}
