import { Inject } from '@midwayjs/core';
import { Job, IJob } from '@midwayjs/cron';
import { OperationLogService } from '../service/operation-log.service';

/**
 * 日志清理任务
 * 使用 Midway Cron 定时任务实现定时清理过期日志
 */
@Job({
  cronTime: '0 0 2 * * *', // 每天凌晨2点执行
  start: true, // 自动启动
  runOnInit: false, // 启动时不立即执行
})
export class LogCleanupTask implements IJob {
  @Inject()
  operationLogService: OperationLogService;

  // 配置参数
  private readonly LOG_RETENTION_DAYS = 90; // 保留90天（3个月）
  private readonly TASK_NAME = 'log-cleanup-task';

  /**
   * 定时任务执行方法
   * 每天凌晨2点自动执行日志清理
   */
  async onTick() {
    await this.executeCleanup();
  }

  /**
   * 执行日志清理
   */
  private async executeCleanup(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🧹 开始执行日志清理任务...');
      
      // 执行清理
      const deletedCount = await this.operationLogService.cleanupExpiredLogs(
        this.LOG_RETENTION_DAYS
      );

      const duration = Date.now() - startTime;

      if (deletedCount > 0) {
        console.log(
          `✅ 日志清理完成：删除了 ${deletedCount} 条超过 ${this.LOG_RETENTION_DAYS} 天的日志记录，耗时 ${duration}ms`
        );
      } else {
        console.log(
          `ℹ️ 日志清理完成：没有需要清理的过期日志，耗时 ${duration}ms`
        );
      }

      // 记录清理统计信息
      await this.logCleanupStats(deletedCount, duration);
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(
        `❌ 日志清理失败：${error.message}，耗时 ${duration}ms`
      );
      
      // 记录清理失败信息
      await this.logCleanupError(error, duration);
    }
  }

  /**
   * 记录清理统计信息
   * @param deletedCount 删除的记录数
   * @param duration 执行时长
   */
  private async logCleanupStats(deletedCount: number, duration: number): Promise<void> {
    try {
      // 可以将清理统计信息记录到专门的统计表或日志系统
      const stats = {
        task_name: this.TASK_NAME,
        execution_time: new Date(),
        deleted_count: deletedCount,
        duration_ms: duration,
        retention_days: this.LOG_RETENTION_DAYS,
        status: 'success',
      };

      // 这里可以扩展为记录到数据库或其他监控系统
      console.log('📊 清理统计:', JSON.stringify(stats, null, 2));
    } catch (error) {
      console.error('记录清理统计失败:', error);
    }
  }

  /**
   * 记录清理错误信息
   * @param error 错误对象
   * @param duration 执行时长
   */
  private async logCleanupError(error: Error, duration: number): Promise<void> {
    try {
      const errorInfo = {
        task_name: this.TASK_NAME,
        execution_time: new Date(),
        error_message: error.message,
        error_stack: error.stack,
        duration_ms: duration,
        status: 'failed',
      };

      // 这里可以扩展为记录到错误监控系统
      console.error('💥 清理错误:', JSON.stringify(errorInfo, null, 2));
    } catch (logError) {
      console.error('记录清理错误失败:', logError);
    }
  }

  /**
   * 手动触发清理任务（用于测试或紧急清理）
   */
  async manualCleanup(): Promise<{ deletedCount: number; duration: number }> {
    const startTime = Date.now();
    
    try {
      console.log('🔧 手动触发日志清理任务...');
      
      const deletedCount = await this.operationLogService.cleanupExpiredLogs(
        this.LOG_RETENTION_DAYS
      );
      
      const duration = Date.now() - startTime;
      
      console.log(
        `✅ 手动清理完成：删除了 ${deletedCount} 条记录，耗时 ${duration}ms`
      );
      
      return { deletedCount, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ 手动清理失败：${error.message}，耗时 ${duration}ms`);
      throw error;
    }
  }

  /**
   * 获取清理任务配置信息
   */
  getTaskInfo() {
    return {
      taskName: this.TASK_NAME,
      cronExpression: '0 0 2 * * *',
      retentionDays: this.LOG_RETENTION_DAYS,
      description: '每天凌晨2点自动清理超过90天的操作日志',
      timezone: 'Asia/Shanghai',
    };
  }
}
