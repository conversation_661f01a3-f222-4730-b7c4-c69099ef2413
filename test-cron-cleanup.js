const axios = require('axios');

// 测试新的定时任务清理架构
async function testCronCleanup() {
  try {
    console.log('🚀 开始测试新的定时任务清理架构...');
    
    // 1. 获取清理任务配置信息
    console.log('\n📋 1. 获取清理任务配置信息...');
    const infoResponse = await axios.get('http://127.0.0.1:3141/api/log-cleanup/info', {
      headers: {
        'x-user-id': 'test_admin',
        'x-user-name': 'admin_user',
      }
    });

    if (infoResponse.data.errCode === 0) {
      const taskInfo = infoResponse.data.data;
      console.log('✅ 任务配置信息:');
      console.log(`   任务名称: ${taskInfo.taskName}`);
      console.log(`   Cron表达式: ${taskInfo.cronExpression}`);
      console.log(`   保留天数: ${taskInfo.retentionDays} 天`);
      console.log(`   描述: ${taskInfo.description}`);
      console.log(`   时区: ${taskInfo.timezone}`);
    } else {
      console.log('❌ 获取任务配置失败:', infoResponse.data.msg);
    }

    // 2. 获取清理任务状态
    console.log('\n📊 2. 获取清理任务状态...');
    const statusResponse = await axios.get('http://127.0.0.1:3141/api/log-cleanup/status', {
      headers: {
        'x-user-id': 'test_admin',
        'x-user-name': 'admin_user',
      }
    });

    if (statusResponse.data.errCode === 0) {
      const status = statusResponse.data.data;
      console.log('✅ 任务状态信息:');
      console.log(`   当前时间: ${status.currentTime}`);
      console.log(`   下次执行: ${status.nextExecution}`);
      console.log(`   距离下次执行: ${status.timeUntilNext}`);
      console.log(`   任务状态: ${status.isActive ? '活跃' : '停止'}`);
    } else {
      console.log('❌ 获取任务状态失败:', statusResponse.data.msg);
    }

    // 3. 获取清理规则说明
    console.log('\n📜 3. 获取清理规则说明...');
    const rulesResponse = await axios.get('http://127.0.0.1:3141/api/log-cleanup/rules', {
      headers: {
        'x-user-id': 'test_admin',
        'x-user-name': 'admin_user',
      }
    });

    if (rulesResponse.data.errCode === 0) {
      const rules = rulesResponse.data.data;
      console.log('✅ 清理规则:');
      console.log(`   保留策略: ${rules.retentionPolicy.description}`);
      console.log(`   执行计划: ${rules.schedule.description}`);
      console.log(`   清理范围: ${rules.scope.description}`);
      console.log(`   安全机制: ${rules.safety.description}`);
      console.log(`   监控机制: ${rules.monitoring.description}`);
    } else {
      console.log('❌ 获取清理规则失败:', rulesResponse.data.msg);
    }

    // 4. 手动触发清理任务
    console.log('\n🔧 4. 手动触发清理任务...');
    const manualResponse = await axios.post('http://127.0.0.1:3141/api/log-cleanup/manual', {}, {
      headers: {
        'Content-Type': 'application/json',
        'x-user-id': 'test_admin',
        'x-user-name': 'admin_user',
      }
    });

    if (manualResponse.data.errCode === 0) {
      const result = manualResponse.data.data;
      console.log('✅ 手动清理执行成功:');
      console.log(`   删除记录数: ${result.deletedCount} 条`);
      console.log(`   执行时长: ${result.duration} ms`);
      
      if (result.deletedCount > 0) {
        console.log('🗑️ 成功清理了过期日志');
      } else {
        console.log('ℹ️ 没有需要清理的过期日志');
      }
    } else {
      console.log('❌ 手动清理执行失败:', manualResponse.data.msg);
    }

    // 5. 获取清理历史
    console.log('\n📈 5. 获取清理历史...');
    const historyResponse = await axios.get('http://127.0.0.1:3141/api/log-cleanup/history', {
      headers: {
        'x-user-id': 'test_admin',
        'x-user-name': 'admin_user',
      }
    });

    if (historyResponse.data.errCode === 0) {
      const history = historyResponse.data.data;
      console.log('✅ 清理历史:');
      console.log(`   总执行次数: ${history.totalExecutions} 次`);
      console.log(`   总删除记录: ${history.totalDeletedRecords} 条`);
      console.log(`   平均执行时长: ${history.averageDuration} ms`);
      console.log(`   成功率: ${history.successRate}%`);
    } else {
      console.log('❌ 获取清理历史失败:', historyResponse.data.msg);
    }

    // 6. 架构对比总结
    console.log('\n🎯 6. 新架构优势总结:');
    console.log('   ✅ 职责分离: 清理逻辑从中间件移到独立的定时任务');
    console.log('   ✅ 时间精确: 使用Cron表达式精确控制执行时间');
    console.log('   ✅ 独立执行: 不依赖用户请求，自动执行');
    console.log('   ✅ 性能友好: 不影响请求处理性能');
    console.log('   ✅ 易于管理: 提供管理接口，便于监控和控制');
    console.log('   ✅ 可扩展性: 便于添加更多定时任务');

    console.log('\n🎉 新的定时任务清理架构测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testCronCleanup();
