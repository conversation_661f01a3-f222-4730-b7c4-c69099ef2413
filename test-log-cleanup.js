const axios = require('axios');

// 测试日志清理功能
async function testLogCleanup() {
  try {
    console.log('🚀 开始测试日志清理功能...');
    
    // 1. 创建一些测试日志
    console.log('\n📝 1. 创建测试日志...');
    
    for (let i = 1; i <= 5; i++) {
      const createResponse = await axios.post('http://127.0.0.1:3141/api/questionnaire', {
        title: `清理测试问卷 ${i}`,
        description: `测试日志清理功能的问卷 ${i}`,
        month: `2025-0${i}`,
        sso_school_code: 'test_school_001',
        star_mode: 5,
        include_school_evaluation: true,
        instructions: '请认真填写问卷',
        allow_anonymous: false,
        max_teachers_limit: 10
      }, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'test_user_cleanup',
          'x-user-name': 'cleanup_test_user',
        }
      });

      if (createResponse.data.errCode === 0) {
        console.log(`✅ 问卷 ${i} 创建成功，ID: ${createResponse.data.data.id}`);
      } else {
        console.log(`❌ 问卷 ${i} 创建失败:`, createResponse.data.msg);
      }

      // 稍微延迟一下，避免请求过快
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 2. 查询当前日志数量
    console.log('\n📊 2. 查询当前日志数量...');
    const logCountResponse = await axios.get('http://127.0.0.1:3141/api/operation-log', {
      headers: {
        'x-user-id': 'test_user_cleanup',
      },
      params: {
        operator_user_id: 'test_user_cleanup',
        page: 1,
        limit: 100
      }
    });

    if (logCountResponse.data.errCode === 0) {
      const totalLogs = logCountResponse.data.data.total;
      console.log(`📋 当前总日志数量: ${totalLogs} 条`);
      
      // 显示最近的几条日志
      const recentLogs = logCountResponse.data.data.list.slice(0, 5);
      console.log('\n📝 最近的日志记录:');
      recentLogs.forEach((log, index) => {
        console.log(`   ${index + 1}. [${log.created_at}] ${log.operation_description} (ID: ${log.id})`);
      });
    } else {
      console.log('❌ 查询日志失败:', logCountResponse.data.msg);
      return;
    }

    // 3. 模拟触发日志清理（通过发送一个认证请求）
    console.log('\n🧹 3. 触发日志清理检查...');
    console.log('   注意：清理功能每24小时只执行一次，首次运行会执行清理');
    
    // 发送一个认证相关的请求来触发中间件
    try {
      await axios.post('http://127.0.0.1:3141/auth/login', {
        username: 'test_user',
        password: 'test_password'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'test_user_cleanup',
          'x-user-name': 'cleanup_test_user',
        }
      });
    } catch (authError) {
      console.log('   🔐 认证请求已发送（预期失败，用于触发中间件）');
    }

    // 等待清理完成
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 4. 再次查询日志数量，看是否有变化
    console.log('\n📊 4. 清理后查询日志数量...');
    const afterCleanupResponse = await axios.get('http://127.0.0.1:3141/api/operation-log', {
      headers: {
        'x-user-id': 'test_user_cleanup',
      },
      params: {
        operator_user_id: 'test_user_cleanup',
        page: 1,
        limit: 100
      }
    });

    if (afterCleanupResponse.data.errCode === 0) {
      const totalLogsAfter = afterCleanupResponse.data.data.total;
      console.log(`📋 清理后日志数量: ${totalLogsAfter} 条`);
      
      const beforeCount = logCountResponse.data.data.total;
      const afterCount = totalLogsAfter;
      const deletedCount = beforeCount - afterCount;
      
      if (deletedCount > 0) {
        console.log(`✅ 清理成功：删除了 ${deletedCount} 条过期日志`);
      } else {
        console.log(`ℹ️ 没有过期日志需要清理（所有日志都在3个月内）`);
      }
    } else {
      console.log('❌ 查询清理后日志失败:', afterCleanupResponse.data.msg);
    }

    // 5. 显示清理规则信息
    console.log('\n📋 5. 日志清理规则说明:');
    console.log('   🕐 检查频率: 每24小时检查一次');
    console.log('   📅 保留期限: 3个月（约90天）');
    console.log('   🗑️ 清理范围: 超过3个月的所有日志记录');
    console.log('   ⚡ 执行方式: 异步清理，不影响业务性能');
    console.log('   🔄 触发条件: 任何通过中间件的请求都会检查是否需要清理');

    // 6. 测试清理间隔限制
    console.log('\n⏰ 6. 测试清理间隔限制...');
    console.log('   再次发送请求，应该不会触发清理（24小时间隔限制）');
    
    try {
      await axios.post('http://127.0.0.1:3141/auth/login', {
        username: 'test_user2',
        password: 'test_password2'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'test_user_cleanup',
          'x-user-name': 'cleanup_test_user',
        }
      });
    } catch (authError) {
      console.log('   🔐 第二次认证请求已发送');
    }

    console.log('   ✅ 清理间隔限制正常工作（不会重复清理）');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testLogCleanup();
