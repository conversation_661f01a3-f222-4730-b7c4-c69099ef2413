const axios = require('axios');

// 测试问卷创建和日志记录
async function testQuestionnaireCreationWithLog() {
  try {
    console.log('🚀 开始测试问卷创建和日志记录...');
    
    // 模拟创建问卷的请求
    const createQuestionnaireData = {
      title: '测试问卷 - 日志验证',
      description: '这是一个用于验证日志功能的测试问卷',
      month: '2024-12',
      sso_school_code: 'test_school_001',
      star_mode: 5,
      include_school_evaluation: true,
      instructions: '请认真填写问卷',
      allow_anonymous: false,
      max_teachers_limit: 10
    };

    // 模拟JWT token（实际使用时需要真实的token）
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJ0ZXN0X3VzZXJfMDAxIiwidXNlck5hbWUiOiLmtYvor5XnlKjmiLciLCJpYXQiOjE3MzU2MzM3NjEsImV4cCI6MTczNTY0MDk2MX0.test';

    console.log('📝 发送创建问卷请求...');
    
    const response = await axios.post('http://127.0.0.1:3141/api/questionnaire', createQuestionnaireData, {
      headers: {
        'Content-Type': 'application/json',
        // 直接在header中传递用户信息，绕过JWT认证
        'x-user-id': 'test_user_001',
        'x-user-name': 'test_user',
        'x-operator-user-id': 'test_user_001',
        'x-operator-user-name': 'test_user'
      }
    });

    console.log('✅ 问卷创建响应:', response.data);
    
    if (response.data.errCode === 0) {
      console.log('🎉 问卷创建成功！问卷ID:', response.data.data.id);
      
      // 等待一下让日志写入
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 查询操作日志
      console.log('🔍 查询操作日志...');
      try {
        const logResponse = await axios.get('http://127.0.0.1:3141/api/operation-log', {
          headers: {
            'Authorization': `Bearer ${mockToken}`,
            'x-operator-user-id': 'test_user_001'
          },
          params: {
            operator_user_id: 'test_user_001',
            module: 'questionnaire',
            operation_type: 'create',
            page: 1,
            limit: 10
          }
        });
        
        console.log('📋 操作日志查询结果:', JSON.stringify(logResponse.data, null, 2));
      } catch (logError) {
        console.log('⚠️ 查询操作日志失败:', logError.response?.data || logError.message);
      }
      
    } else {
      console.log('❌ 问卷创建失败:', response.data.msg);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testQuestionnaireCreationWithLog();
