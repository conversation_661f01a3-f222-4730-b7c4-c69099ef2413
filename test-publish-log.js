const axios = require('axios');

// 测试问卷发布和日志记录
async function testQuestionnairePublishWithLog() {
  try {
    console.log('🚀 开始测试问卷发布和日志记录...');
    
    // 首先获取一个草稿状态的问卷
    console.log('📋 查询草稿状态的问卷...');
    const listResponse = await axios.get('http://127.0.0.1:3141/api/questionnaire', {
      headers: {
        'x-user-id': 'test_user_001',
        'x-user-name': 'test_user',
      },
      params: {
        status: 'draft',
        page: 1,
        limit: 1
      }
    });

    if (listResponse.data.errCode !== 0 || listResponse.data.data.list.length === 0) {
      console.log('❌ 没有找到草稿状态的问卷');
      return;
    }

    const questionnaire = listResponse.data.data.list[0];
    console.log('✅ 找到草稿问卷:', questionnaire.id, questionnaire.title);

    // 发布问卷
    console.log('📤 发布问卷...');
    const publishResponse = await axios.put(`http://127.0.0.1:3141/api/questionnaire/${questionnaire.id}/status`, {
      status: 'published'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-user-id': 'test_user_001',
        'x-user-name': 'test_user',
      }
    });

    console.log('✅ 问卷发布响应:', publishResponse.data);
    
    if (publishResponse.data.errCode === 0) {
      console.log('🎉 问卷发布成功！');
      
      // 等待一下让日志写入
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 查询操作日志
      console.log('🔍 查询发布操作日志...');
      try {
        const logResponse = await axios.get('http://127.0.0.1:3141/api/operation-log', {
          headers: {
            'x-user-id': 'test_user_001',
          },
          params: {
            operator_user_id: 'test_user_001',
            module: 'questionnaire',
            operation_type: 'publish',
            page: 1,
            limit: 5
          }
        });
        
        console.log('📋 发布操作日志:', JSON.stringify(logResponse.data, null, 2));
      } catch (logError) {
        console.log('⚠️ 查询操作日志失败:', logError.response?.data || logError.message);
      }
      
    } else {
      console.log('❌ 问卷发布失败:', publishResponse.data.msg);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testQuestionnairePublishWithLog();
